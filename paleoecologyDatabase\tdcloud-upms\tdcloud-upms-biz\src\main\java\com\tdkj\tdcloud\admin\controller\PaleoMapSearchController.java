/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.controller;

import com.tdkj.tdcloud.admin.api.dto.PaleoMapSearchDTO;
import com.tdkj.tdcloud.admin.service.PaleoMapSearchService;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;


import java.util.List;

/**
 * Paleo Map Search Controller - Equivalent to Flask API functionality
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/paleo-map")
@Tag(description = "paleo-map", name = "古生物地图搜索管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PaleoMapSearchController {

    private final PaleoMapSearchService paleoMapSearchService;

    /**
     * Complex map search with geographic and taxonomic filters
     * Equivalent to Flask /data endpoint
     */
    @Inner(value = false)
    @Operation(summary = "地图数据搜索", description = "复杂的地理和分类学搜索")
    @GetMapping("/data")
    public R searchMapData(
            @RequestParam(value = "search_term", required = false) List<String> searchTerms,
            @RequestParam(value = "collection_name", required = false) String collectionName,
            @RequestParam(value = "polygons", required = false) String polygons,
            @RequestParam(value = "bboxes", required = false) String bboxes,
            @RequestParam(value = "circle_centers", required = false) List<String> circleCenters,
            @RequestParam(value = "circle_radii", required = false) List<String> circleRadii,
            
            // Taxonomic parameters
            @RequestParam(value = "acceptedRank", required = false) String acceptedRank,
            @RequestParam(value = "phylum", required = false) String phylum,
            @RequestParam(value = "class", required = false) String classField,
            @RequestParam(value = "order", required = false) String orderField,
            @RequestParam(value = "family", required = false) String family,
            @RequestParam(value = "genus", required = false) String genus,
            @RequestParam(value = "species", required = false) String species,
            @RequestParam(value = "scientificName", required = false) String scientificName,
            @RequestParam(value = "originalName", required = false) String originalName,
            
            // Time parameters
            @RequestParam(value = "epoch", required = false) String epoch,
            @RequestParam(value = "stage", required = false) String stage,
            @RequestParam(value = "earlyInterval", required = false) String earlyInterval,
            @RequestParam(value = "lateInterval", required = false) String lateInterval,
            @RequestParam(value = "timeBin", required = false) String timeBin,
            
            // Geological age parameters
            @RequestParam(value = "ageMin", required = false) String ageMin,
            @RequestParam(value = "ageMax", required = false) String ageMax,
            
            // Location parameters
            @RequestParam(value = "country", required = false) String country,
            @RequestParam(value = "southLatitude", defaultValue = "-90") String southLatitude,
            @RequestParam(value = "northLatitude", defaultValue = "90") String northLatitude,
            @RequestParam(value = "westLongitude", defaultValue = "-180") String westLongitude,
            @RequestParam(value = "eastLongitude", defaultValue = "180") String eastLongitude,
            @RequestParam(value = "datingMethod", required = false) String datingMethod,
            @RequestParam(value = "datingQuality", required = false) String datingQuality,
            
            // Additional parameters
            @RequestParam(value = "author", required = false) String author,
            @RequestParam(value = "pubyr", required = false) String pubyr,
            @RequestParam(value = "fossilType", required = false) String fossilType,
            @RequestParam(value = "plantOrgan", required = false) String plantOrgan) {

        try {
            // Build search DTO
            PaleoMapSearchDTO searchDTO = new PaleoMapSearchDTO();
            searchDTO.setSearchTerms(searchTerms);
            searchDTO.setCollectionName(collectionName);
            searchDTO.setPolygons(polygons);
            searchDTO.setBboxes(bboxes);
            searchDTO.setCircleCenters(circleCenters);
            searchDTO.setCircleRadii(circleRadii);
            
            // Set taxonomic parameters
            searchDTO.setAcceptedRank(acceptedRank);
            searchDTO.setPhylum(phylum);
            searchDTO.setClassField(classField);
            searchDTO.setOrderField(orderField);
            searchDTO.setFamily(family);
            searchDTO.setGenus(genus);
            searchDTO.setSpecies(species);
            searchDTO.setScientificName(scientificName);
            searchDTO.setOriginalName(originalName);
            
            // Set time parameters
            searchDTO.setEpoch(epoch);
            searchDTO.setStage(stage);
            searchDTO.setEarlyInterval(earlyInterval);
            searchDTO.setLateInterval(lateInterval);
            searchDTO.setTimeBin(timeBin);
            searchDTO.setAgeMin(ageMin);
            searchDTO.setAgeMax(ageMax);
            
            // Set location parameters
            searchDTO.setCountry(country);
            searchDTO.setSouthLatitude(southLatitude);
            searchDTO.setNorthLatitude(northLatitude);
            searchDTO.setWestLongitude(westLongitude);
            searchDTO.setEastLongitude(eastLongitude);
            searchDTO.setDatingMethod(datingMethod);
            searchDTO.setDatingQuality(datingQuality);
            
            // Set additional parameters
            searchDTO.setAuthor(author);
            searchDTO.setPubyr(pubyr);
            searchDTO.setFossilType(fossilType);
            searchDTO.setPlantOrgan(plantOrgan);

            log.debug("Received map search request with {} parameters", 
                     searchDTO.toString().split(",").length);

            return paleoMapSearchService.searchMapData(searchDTO);

        } catch (Exception e) {
            log.error("Error in map search: ", e);
            return R.failed("Search failed: " + e.getMessage());
        }
    }

    /**
     * Get detail data from Chattian table by ID
     * Equivalent to Flask /detail endpoint
     */
    @Inner(value = false)
    @Operation(summary = "获取详细数据", description = "根据ID获取Chattian表的详细数据")
    @GetMapping("/detail")
    public R getDetailData(@RequestParam("id") String id) {
        return paleoMapSearchService.getDetailData(id);
    }

    /**
     * Get unique dating method values
     * Equivalent to Flask /dating-methods endpoint
     */
    @Inner(value = false)
    @Operation(summary = "获取年代测定方法", description = "获取所有唯一的年代测定方法")
    @GetMapping("/dating-methods")
    public R getDatingMethods() {
        return paleoMapSearchService.getDatingMethods();
    }

    /**
     * Get unique dating quality values
     * Equivalent to Flask /dating-qualities endpoint
     */
    @Inner(value = false)
    @Operation(summary = "获取年代测定质量", description = "获取所有唯一的年代测定质量")
    @GetMapping("/dating-qualities")
    public R getDatingQualities() {
        return paleoMapSearchService.getDatingQualities();
    }

    /**
     * Get unique family values
     * Equivalent to Flask /taxa/families endpoint
     */
    @Inner(value = false)
    @Operation(summary = "获取科名", description = "获取所有唯一的科名")
    @GetMapping("/taxa/families")
    public R getFamilies() {
        return paleoMapSearchService.getFamilies();
    }

    /**
     * Get unique genus values
     * Equivalent to Flask /taxa/genera endpoint
     */
    @Inner(value = false)
    @Operation(summary = "获取属名", description = "获取所有唯一的属名")
    @GetMapping("/taxa/genera")
    public R getGenera() {
        return paleoMapSearchService.getGenera();
    }

    /**
     * Get unique species values
     * Equivalent to Flask /taxa/species endpoint
     */
    @Inner(value = false)
    @Operation(summary = "获取种名", description = "获取所有唯一的种名")
    @GetMapping("/taxa/species")
    public R getSpecies() {
        return paleoMapSearchService.getSpecies();
    }

    /**
     * Get unique scientific name values
     * Equivalent to Flask /taxa/scientific-names endpoint
     */
    @Inner(value = false)
    @Operation(summary = "获取学名", description = "获取所有唯一的学名")
    @GetMapping("/taxa/scientific-names")
    public R getScientificNames() {
        return paleoMapSearchService.getScientificNames();
    }

    /**
     * Get unique original name values
     * Equivalent to Flask /taxa/original-names endpoint
     */
    @Inner(value = false)
    @Operation(summary = "获取原始名称", description = "获取所有唯一的原始名称")
    @GetMapping("/taxa/original-names")
    public R getOriginalNames() {
        return paleoMapSearchService.getOriginalNames();
    }

    /**
     * Get unique fossil type values
     * Equivalent to Flask /fossil-types endpoint
     */
    @Inner(value = false)
    @Operation(summary = "获取化石类型", description = "获取所有唯一的化石类型")
    @GetMapping("/fossil-types")
    public R getFossilTypes() {
        return paleoMapSearchService.getFossilTypes();
    }

    /**
     * Get unique plant organ values
     * Equivalent to Flask /plant-organs endpoint
     */
    @Inner(value = false)
    @Operation(summary = "获取植物器官", description = "获取所有唯一的植物器官")
    @GetMapping("/plant-organs")
    public R getPlantOrgans() {
        return paleoMapSearchService.getPlantOrgans();
    }
}
