/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.service.impl;

import com.tdkj.tdcloud.admin.api.dto.PaleoMapSearchDTO;
import com.tdkj.tdcloud.admin.mapper.ChattianMapper;
import com.tdkj.tdcloud.admin.mapper.RupelianMapper;
import com.tdkj.tdcloud.admin.service.PaleoMapSearchService;
import com.tdkj.tdcloud.admin.utils.GeographicUtils;
import com.tdkj.tdcloud.common.core.util.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Paleo Map Search Service Implementation
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaleoMapSearchServiceImpl implements PaleoMapSearchService {

    private final RupelianMapper rupelianMapper;
    private final ChattianMapper chattianMapper;

    @Override
    public R searchMapData(PaleoMapSearchDTO searchDTO) {
        try {
            log.debug("Starting map search with parameters: {}", searchDTO);

            // Execute query using MyBatis mapper method
            List<Map<String, Object>> results = rupelianMapper.searchMapDataWithFilters(searchDTO);
            log.debug("Query executed successfully, fetched {} results", results.size());

            // Process geographic filtering
            List<Map<String, Object>> filteredResults = processGeographicFiltering(results, searchDTO);

            // Process and merge multi-level fields
            List<Map<String, Object>> processedResults = processResults(filteredResults);

            log.debug("Final results: {} records", processedResults.size());

            // Create result map compatible with JDK 1.8
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("table1", processedResults);
            return R.ok(resultMap);

        } catch (Exception e) {
            log.error("Error in map search: ", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getDetailData(String id) {
        try {
            if (!StringUtils.hasText(id)) {
                return R.failed("ID parameter is required");
            }

            List<Map<String, Object>> detailData = chattianMapper.getDetailById(id);

            // Create result map compatible with JDK 1.8
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("detail", detailData);
            return R.ok(resultMap);

        } catch (Exception e) {
            log.error("Error getting detail data for ID {}: ", id, e);
            return R.failed("Error retrieving detail data: " + e.getMessage());
        }
    }

    @Override
    public R getDatingMethods() {
        try {
            List<String> methods = rupelianMapper.getUniqueDatingMethods();
            return R.ok(methods);
        } catch (Exception e) {
            log.error("Error fetching dating methods: ", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getDatingQualities() {
        try {
            List<String> qualities = rupelianMapper.getUniqueDatingQualities();
            return R.ok(qualities);
        } catch (Exception e) {
            log.error("Error fetching dating qualities: ", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getFamilies() {
        try {
            List<String> families = chattianMapper.getUniqueFamilies();
            return R.ok(families);
        } catch (Exception e) {
            log.error("Error fetching families: ", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getGenera() {
        try {
            List<String> genera = chattianMapper.getUniqueGenera();
            return R.ok(genera);
        } catch (Exception e) {
            log.error("Error fetching genera: ", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getSpecies() {
        try {
            List<String> species = chattianMapper.getUniqueSpecies();
            return R.ok(species);
        } catch (Exception e) {
            log.error("Error fetching species: ", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getScientificNames() {
        try {
            List<String> names = chattianMapper.getUniqueScientificNames();
            return R.ok(names);
        } catch (Exception e) {
            log.error("Error fetching scientific names: ", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getOriginalNames() {
        try {
            List<String> names = chattianMapper.getUniqueOriginalNames();
            return R.ok(names);
        } catch (Exception e) {
            log.error("Error fetching original names: ", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getFossilTypes() {
        try {
            List<String> types = rupelianMapper.getUniqueFossilTypes();
            return R.ok(types);
        } catch (Exception e) {
            log.error("Error fetching fossil types: ", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getPlantOrgans() {
        try {
            List<String> organs = chattianMapper.getUniquePlantOrgans();
            return R.ok(organs);
        } catch (Exception e) {
            log.error("Error fetching plant organs: ", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }





    /**
     * Process geographic filtering for polygons, circles, and bounding boxes
     */
    private List<Map<String, Object>> processGeographicFiltering(List<Map<String, Object>> results, PaleoMapSearchDTO searchDTO) {
        // Parse geographic shapes
        List<List<double[]>> polygons = parsePolygons(searchDTO.getPolygons());
        List<double[]> bboxes = parseBoundingBoxes(searchDTO.getBboxes());
        List<CircleData> circles = parseCircles(searchDTO.getCircleCenters(), searchDTO.getCircleRadii());

        // If no geographic shapes defined, return all results
        if (polygons.isEmpty() && bboxes.isEmpty() && circles.isEmpty()) {
            return results;
        }

        List<Map<String, Object>> filteredResults = new ArrayList<>();
        int originalCount = results.size();
        int filteredCount = 0;

        for (Map<String, Object> row : results) {
            try {
                double lng = Double.parseDouble(String.valueOf(row.get("Longitude")));
                double lat = Double.parseDouble(String.valueOf(row.get("Latitude")));
                double[] point = {lng, lat};

                boolean inAnyShape = false;

                // Check polygons
                for (List<double[]> polygon : polygons) {
                    if (GeographicUtils.pointInPolygon(point, polygon)) {
                        inAnyShape = true;
                        log.debug("Point at ({}, {}) is inside a polygon", lng, lat);
                        break;
                    }
                }

                // Check circles if not in polygon
                if (!inAnyShape) {
                    for (CircleData circle : circles) {
                        if (GeographicUtils.pointInCircle(point, circle.centerLng, circle.centerLat, circle.radiusMeters)) {
                            inAnyShape = true;
                            log.debug("Point at ({}, {}) is inside a circle", lng, lat);
                            break;
                        }
                    }
                }

                // Check bounding boxes if not in other shapes
                if (!inAnyShape) {
                    for (double[] bbox : bboxes) {
                        if (GeographicUtils.pointInBoundingBox(point, bbox[0], bbox[1], bbox[2], bbox[3])) {
                            inAnyShape = true;
                            log.debug("Point at ({}, {}) is inside a bounding box", lng, lat);
                            break;
                        }
                    }
                }

                if (inAnyShape) {
                    filteredResults.add(row);
                } else {
                    filteredCount++;
                    log.debug("Filtered out point at ({}, {}) - outside all shapes", lng, lat);
                }

            } catch (Exception e) {
                log.error("Error checking point in shapes: ", e);
                // Include problematic rows to avoid data loss
                filteredResults.add(row);
            }
        }

        log.debug("Original results: {}, Filtered out: {}, Final results: {}",
                 originalCount, filteredCount, filteredResults.size());
        return filteredResults;
    }

    /**
     * Process results and merge multi-level fields
     */
    private List<Map<String, Object>> processResults(List<Map<String, Object>> results) {
        return results.stream().map(row -> {
            Map<String, Object> processedRow = new HashMap<>(row);

            // Merge multi-level fields
            for (String baseField : Arrays.asList("Species", "ScientificName", "PlantOrgan")) {
                List<String> values = new ArrayList<>();
                for (int i = 1; i <= 3; i++) {
                    Object value = row.get(baseField + i);
                    if (value != null && StringUtils.hasText(value.toString())) {
                        values.add(value.toString());
                    }
                }
                processedRow.put(baseField, values.isEmpty() ? null : String.join(", ", values));
            }

            return processedRow;
        }).collect(Collectors.toList());
    }

    /**
     * Parse polygon coordinates from string
     */
    private List<List<double[]>> parsePolygons(String polygons) {
        List<List<double[]>> polygonList = new ArrayList<>();
        if (!StringUtils.hasText(polygons)) {
            return polygonList;
        }

        String[] polygonStrings = polygons.split("\\|");
        for (String polygonCoords : polygonStrings) {
            if (StringUtils.hasText(polygonCoords.trim())) {
                try {
                    String[] coords = polygonCoords.split(",");
                    if (coords.length >= 6 && coords.length % 2 == 0) {
                        List<double[]> points = new ArrayList<>();
                        for (int i = 0; i < coords.length; i += 2) {
                            double lng = Double.parseDouble(coords[i].trim());
                            double lat = Double.parseDouble(coords[i + 1].trim());
                            points.add(new double[]{lng, lat});
                        }

                        // Normalize polygon
                        List<double[]> normalizedPolygon = GeographicUtils.normalizePolygon(points);
                        polygonList.add(normalizedPolygon);

                        log.debug("Parsed polygon with {} vertices", normalizedPolygon.size());
                    }
                } catch (NumberFormatException e) {
                    log.error("Error parsing polygon coordinates: {}", e.getMessage());
                }
            }
        }
        return polygonList;
    }

    /**
     * Parse bounding box coordinates from string
     */
    private List<double[]> parseBoundingBoxes(String bboxes) {
        List<double[]> bboxList = new ArrayList<>();
        if (!StringUtils.hasText(bboxes)) {
            return bboxList;
        }

        String[] bboxStrings = bboxes.split("\\|");
        for (String bbox : bboxStrings) {
            if (StringUtils.hasText(bbox.trim())) {
                try {
                    String[] coords = bbox.split(",");
                    if (coords.length == 4) {
                        double minLng = Double.parseDouble(coords[0].trim());
                        double minLat = Double.parseDouble(coords[1].trim());
                        double maxLng = Double.parseDouble(coords[2].trim());
                        double maxLat = Double.parseDouble(coords[3].trim());
                        bboxList.add(new double[]{minLng, minLat, maxLng, maxLat});

                        log.debug("Parsed bounding box: [{}, {}, {}, {}]", minLng, minLat, maxLng, maxLat);
                    }
                } catch (NumberFormatException e) {
                    log.error("Error parsing bounding box coordinates: {}", e.getMessage());
                }
            }
        }
        return bboxList;
    }

    /**
     * Parse circle data from centers and radii
     */
    private List<CircleData> parseCircles(List<String> circleCenters, List<String> circleRadii) {
        List<CircleData> circles = new ArrayList<>();
        if (circleCenters == null || circleRadii == null) {
            return circles;
        }

        // Process pipe-separated centers if needed
        List<String> centers = new ArrayList<>();
        for (String center : circleCenters) {
            if (center.contains("|")) {
                centers.addAll(Arrays.asList(center.split("\\|")));
            } else {
                centers.add(center);
            }
        }

        // Process pipe-separated radii if needed
        List<String> radii = new ArrayList<>();
        for (String radius : circleRadii) {
            if (radius.contains("|")) {
                radii.addAll(Arrays.asList(radius.split("\\|")));
            } else {
                radii.add(radius);
            }
        }

        // Match centers with radii
        for (int i = 0; i < centers.size() && i < radii.size(); i++) {
            try {
                String centerStr = centers.get(i).trim();
                String radiusStr = radii.get(i).trim();

                if (StringUtils.hasText(centerStr) && StringUtils.hasText(radiusStr)) {
                    String[] centerCoords = centerStr.split(",");
                    if (centerCoords.length == 2) {
                        double lng = Double.parseDouble(centerCoords[0].trim());
                        double lat = Double.parseDouble(centerCoords[1].trim());
                        double radiusMeters = Double.parseDouble(radiusStr);

                        if (radiusMeters > 0) {
                            circles.add(new CircleData(lng, lat, radiusMeters));
                            log.debug("Parsed circle: center=({}, {}), radius={}m", lng, lat, radiusMeters);
                        }
                    }
                }
            } catch (NumberFormatException e) {
                log.error("Error parsing circle coordinates: {}", e.getMessage());
            }
        }
        return circles;
    }

    /**
     * Inner class to hold circle data
     */
    private static class CircleData {
        final double centerLng;
        final double centerLat;
        final double radiusMeters;

        CircleData(double centerLng, double centerLat, double radiusMeters) {
            this.centerLng = centerLng;
            this.centerLat = centerLat;
            this.radiusMeters = radiusMeters;
        }
    }
}
